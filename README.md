# Document Intelligence Preview App

A Streamlit application that provides dual previews of documents and their extracted data using Azure Document Intelligence.

## Features

- 📄 **Document Preview**: View PDFs, images, and other supported document types
- 🤖 **AI Analysis**: Extract text, tables, and key-value pairs using Azure Document Intelligence
- 📁 **Simple File Selection**: Choose from documents in your folder via dropdown
- 🔍 **Structured Results**: View extracted content in organized sections

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Azure Credentials**:
   
   **Option A: Environment Variables**
   ```bash
   export AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT="https://your-resource.cognitiveservices.azure.com/"
   export AZURE_DOCUMENT_INTELLIGENCE_KEY="your-api-key"
   ```
   
   **Option B: Streamlit Secrets**
   Create `.streamlit/secrets.toml`:
   ```toml
   AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT = "https://your-resource.cognitiveservices.azure.com/"
   AZURE_DOCUMENT_INTELLIGENCE_KEY = "your-api-key"
   ```

3. **Run the App**:
   ```bash
   streamlit run streamlit_app.py
   ```

## Supported File Types

- PDF (.pdf)
- Images (.png, .jpg, .jpeg)
- Word Documents (.docx)
- OpenDocument Text (.odt)

## Usage

1. Select a document from the dropdown in the sidebar
2. View the document preview in the left column
3. Click "Analyze Document" to extract data with Azure Document Intelligence
4. Review the extracted text, tables, and key-value pairs in the right column

## Document Folder

The app looks for documents in the `Financial Statements - Sample Files (2)` folder. Make sure your documents are placed in this directory.
