import streamlit as st
import os
import base64
from pathlib import Path
from typing import Optional, Dict, Any
import json
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.core.credentials import AzureKeyCredential
import tempfile
import shutil

# Page configuration
st.set_page_config(
    page_title="Document Intelligence Preview",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Constants
DOCUMENTS_FOLDER = "Financial Statements - Sample Files (2)"
SUPPORTED_EXTENSIONS = {'.pdf', '.png', '.jpg', '.jpeg', '.docx', '.odt'}

def get_document_files():
    """Get list of supported document files from the documents folder."""
    documents_path = Path(DOCUMENTS_FOLDER)
    if not documents_path.exists():
        return []
    
    files = []
    for file_path in documents_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_EXTENSIONS:
            files.append(file_path.name)
    
    return sorted(files)

def display_pdf(file_path: Path):
    """Display PDF file in Streamlit."""
    try:
        with open(file_path, "rb") as f:
            base64_pdf = base64.b64encode(f.read()).decode('utf-8')
        
        pdf_display = f"""
        <iframe src="data:application/pdf;base64,{base64_pdf}" 
                width="100%" height="600" type="application/pdf">
        </iframe>
        """
        st.markdown(pdf_display, unsafe_allow_html=True)
    except Exception as e:
        st.error(f"Error displaying PDF: {str(e)}")

def display_image(file_path: Path):
    """Display image file in Streamlit."""
    try:
        st.image(str(file_path), use_column_width=True)
    except Exception as e:
        st.error(f"Error displaying image: {str(e)}")

def display_document(file_path: Path):
    """Display document based on file type."""
    file_extension = file_path.suffix.lower()
    
    if file_extension == '.pdf':
        display_pdf(file_path)
    elif file_extension in {'.png', '.jpg', '.jpeg'}:
        display_image(file_path)
    elif file_extension in {'.docx', '.odt'}:
        st.info(f"Document: {file_path.name}")
        st.write("📄 Document preview not available for this file type, but it can be processed by Document Intelligence.")
    else:
        st.warning(f"Unsupported file type: {file_extension}")

def get_azure_client():
    """Initialize Azure Document Intelligence client."""
    endpoint = st.secrets.get("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT") or os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
    key = st.secrets.get("AZURE_DOCUMENT_INTELLIGENCE_KEY") or os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
    
    if not endpoint or not key:
        return None
    
    return DocumentIntelligenceClient(
        endpoint=endpoint,
        credential=AzureKeyCredential(key)
    )

def analyze_document(file_path: Path, client: DocumentIntelligenceClient) -> Optional[Dict[Any, Any]]:
    """Analyze document using Azure Document Intelligence."""
    try:
        with open(file_path, "rb") as f:
            document_content = f.read()
        
        # Start analysis
        poller = client.begin_analyze_document(
            "prebuilt-layout",  # Using prebuilt layout model
            document_content,
            content_type="application/octet-stream"
        )
        
        # Get result
        result = poller.result()
        
        # Convert to dictionary for easier handling
        return result.as_dict() if hasattr(result, 'as_dict') else dict(result)
        
    except Exception as e:
        st.error(f"Error analyzing document: {str(e)}")
        return None

def display_analysis_results(analysis_result: Dict[Any, Any]):
    """Display the analysis results in a structured format."""
    if not analysis_result:
        st.warning("No analysis results to display.")
        return
    
    # Display content
    if 'content' in analysis_result:
        st.subheader("📝 Extracted Text")
        with st.expander("View extracted text", expanded=False):
            st.text_area("Content", analysis_result['content'], height=200)
    
    # Display tables
    if 'tables' in analysis_result and analysis_result['tables']:
        st.subheader("📊 Tables")
        for i, table in enumerate(analysis_result['tables']):
            st.write(f"**Table {i+1}:**")
            if 'cells' in table:
                # Create a simple table display
                table_data = {}
                for cell in table['cells']:
                    row = cell.get('row_index', 0)
                    col = cell.get('column_index', 0)
                    content = cell.get('content', '')
                    
                    if row not in table_data:
                        table_data[row] = {}
                    table_data[row][col] = content
                
                # Convert to list of lists for display
                if table_data:
                    max_cols = max(max(row.keys()) for row in table_data.values()) + 1
                    table_list = []
                    for row_idx in sorted(table_data.keys()):
                        row_data = []
                        for col_idx in range(max_cols):
                            row_data.append(table_data[row_idx].get(col_idx, ''))
                        table_list.append(row_data)
                    
                    st.table(table_list)
            st.write("---")
    
    # Display key-value pairs
    if 'key_value_pairs' in analysis_result and analysis_result['key_value_pairs']:
        st.subheader("🔑 Key-Value Pairs")
        for kv in analysis_result['key_value_pairs']:
            key = kv.get('key', {}).get('content', 'Unknown Key')
            value = kv.get('value', {}).get('content', 'No Value') if kv.get('value') else 'No Value'
            st.write(f"**{key}:** {value}")
    
    # Display raw JSON for debugging
    with st.expander("🔍 Raw Analysis Results (JSON)", expanded=False):
        st.json(analysis_result)

def main():
    """Main Streamlit application."""
    st.title("📄 Document Intelligence Preview")
    st.markdown("Select a document to preview it and see the extracted data from Azure Document Intelligence.")
    
    # Sidebar for file selection
    st.sidebar.header("📁 Document Selection")
    
    # Get available files
    available_files = get_document_files()
    
    if not available_files:
        st.error(f"No supported documents found in '{DOCUMENTS_FOLDER}' folder.")
        st.info("Supported file types: PDF, PNG, JPG, JPEG, DOCX, ODT")
        return
    
    # File selection
    selected_file = st.sidebar.selectbox(
        "Choose a document:",
        available_files,
        index=0
    )
    
    if not selected_file:
        st.info("Please select a document to preview.")
        return
    
    file_path = Path(DOCUMENTS_FOLDER) / selected_file
    
    # Display file info
    st.sidebar.write(f"**Selected:** {selected_file}")
    st.sidebar.write(f"**Size:** {file_path.stat().st_size / 1024:.1f} KB")
    
    # Main content area with two columns
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📄 Document Preview")
        display_document(file_path)
    
    with col2:
        st.header("🤖 Document Intelligence Analysis")
        
        # Check if Azure credentials are available
        client = get_azure_client()
        
        if not client:
            st.warning("⚠️ Azure Document Intelligence credentials not configured.")
            st.info("""
            To enable document analysis, please set up your Azure credentials:
            
            **Option 1: Environment Variables**
            ```
            AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=your_endpoint
            AZURE_DOCUMENT_INTELLIGENCE_KEY=your_key
            ```
            
            **Option 2: Streamlit Secrets**
            Add to `.streamlit/secrets.toml`:
            ```
            AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT = "your_endpoint"
            AZURE_DOCUMENT_INTELLIGENCE_KEY = "your_key"
            ```
            """)
            return
        
        # Analyze button
        if st.button("🔍 Analyze Document", type="primary"):
            with st.spinner("Analyzing document..."):
                analysis_result = analyze_document(file_path, client)
                
                if analysis_result:
                    st.success("✅ Analysis completed!")
                    display_analysis_results(analysis_result)
                else:
                    st.error("❌ Analysis failed. Please check your credentials and try again.")

if __name__ == "__main__":
    main()
