import streamlit as st
import os
import base64
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
import json
import pandas as pd
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.core.credentials import AzureKeyCredential
import tempfile
import shutil
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="Document Intelligence Preview",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Constants
DOCUMENTS_FOLDER = "Financial Statements - Sample Files (2)"
SUPPORTED_EXTENSIONS = {'.pdf', '.png', '.jpg', '.jpeg', '.docx', '.odt'}

def get_document_files():
    """Get list of supported document files from the documents folder."""
    documents_path = Path(DOCUMENTS_FOLDER)
    if not documents_path.exists():
        return []
    
    files = []
    for file_path in documents_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_EXTENSIONS:
            files.append(file_path.name)
    
    return sorted(files)

def display_pdf(file_path: Path):
    """Display PDF file in Streamlit."""
    try:
        with open(file_path, "rb") as f:
            base64_pdf = base64.b64encode(f.read()).decode('utf-8')
        
        pdf_display = f"""
        <iframe src="data:application/pdf;base64,{base64_pdf}" 
                width="100%" height="600" type="application/pdf">
        </iframe>
        """
        st.markdown(pdf_display, unsafe_allow_html=True)
    except Exception as e:
        st.error(f"Error displaying PDF: {str(e)}")

def display_image(file_path: Path):
    """Display image file in Streamlit."""
    try:
        st.image(str(file_path), use_column_width=True)
    except Exception as e:
        st.error(f"Error displaying image: {str(e)}")

def display_document(file_path: Path):
    """Display document based on file type."""
    file_extension = file_path.suffix.lower()
    
    if file_extension == '.pdf':
        display_pdf(file_path)
    elif file_extension in {'.png', '.jpg', '.jpeg'}:
        display_image(file_path)
    elif file_extension in {'.docx', '.odt'}:
        st.info(f"Document: {file_path.name}")
        st.write("📄 Document preview not available for this file type, but it can be processed by Document Intelligence.")
    else:
        st.warning(f"Unsupported file type: {file_extension}")

# Enhanced Document Intelligence Processing Functions

def get_table_page_numbers(table):
    """Returns a list of page numbers where the table appears."""
    if hasattr(table, 'bounding_regions') and table.bounding_regions:
        return [region.page_number for region in table.bounding_regions]
    return [1]  # Default to page 1 if no bounding regions

def get_table_span_offsets(table):
    """Calculates the minimum and maximum offsets of a table's spans."""
    if not hasattr(table, 'spans') or not table.spans:
        return 0, 0

    min_offset = table.spans[0].offset
    max_offset = table.spans[0].offset + table.spans[0].length

    for span in table.spans:
        if span.offset < min_offset:
            min_offset = span.offset
        if span.offset + span.length > max_offset:
            max_offset = span.offset + span.length

    return min_offset, max_offset

def find_merge_table_candidates(tables):
    """Finds tables that might be split across pages and should be merged."""
    merge_candidates = []

    for i in range(len(tables) - 1):
        current_table = tables[i]
        next_table = tables[i + 1]

        current_pages = get_table_page_numbers(current_table)
        next_pages = get_table_page_numbers(next_table)

        # Check if tables are on consecutive pages
        if current_pages and next_pages:
            current_max_page = max(current_pages)
            next_min_page = min(next_pages)

            # If next table is on the immediately following page
            if next_min_page == current_max_page + 1:
                # Check if column counts match
                current_cols = getattr(current_table, 'column_count', 0)
                next_cols = getattr(next_table, 'column_count', 0)

                if current_cols == next_cols and current_cols > 0:
                    merge_candidates.append({
                        'table1_idx': i,
                        'table2_idx': i + 1,
                        'page_break': f"{current_max_page}-{next_min_page}"
                    })

    return merge_candidates

def check_paragraph_presence(paragraphs, start_offset, end_offset):
    """Checks if there are meaningful paragraphs between table segments."""
    if not paragraphs:
        return False

    for paragraph in paragraphs:
        if hasattr(paragraph, 'spans') and paragraph.spans:
            for span in paragraph.spans:
                if start_offset < span.offset < end_offset:
                    # Check if paragraph has a role that indicates it's not just formatting
                    if hasattr(paragraph, 'role'):
                        if paragraph.role not in ["pageHeader", "pageFooter", "pageNumber"]:
                            return True
                    else:
                        return True
    return False

def get_azure_client():
    """Initialize Azure Document Intelligence client."""
    # Try environment variables first, then Streamlit secrets as fallback
    endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
    key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")

    # Fallback to Streamlit secrets if env vars not found
    if not endpoint or not key:
        try:
            endpoint = endpoint or st.secrets.get("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
            key = key or st.secrets.get("AZURE_DOCUMENT_INTELLIGENCE_KEY")
        except:
            # If secrets.toml doesn't exist, just continue with None values
            pass

    if not endpoint or not key:
        return None

    return DocumentIntelligenceClient(
        endpoint=endpoint,
        credential=AzureKeyCredential(key)
    )

def convert_table_to_dataframe(table):
    """Convert Document Intelligence table to pandas DataFrame."""
    if not hasattr(table, 'cells') or not table.cells:
        return pd.DataFrame()

    # Get table dimensions
    max_row = max(cell.row_index for cell in table.cells) + 1
    max_col = max(cell.column_index for cell in table.cells) + 1

    # Initialize empty table
    table_data = [["" for _ in range(max_col)] for _ in range(max_row)]

    # Fill table with cell content
    for cell in table.cells:
        content = getattr(cell, 'content', '')
        row_idx = cell.row_index
        col_idx = cell.column_index
        table_data[row_idx][col_idx] = content

    # Convert to DataFrame
    df = pd.DataFrame(table_data)

    # Use first row as headers if it looks like headers
    if len(df) > 1:
        # Check if first row contains mostly text and subsequent rows contain numbers/mixed content
        first_row_text_ratio = sum(1 for cell in df.iloc[0] if isinstance(cell, str) and not cell.replace('.', '').replace(',', '').replace('$', '').replace('%', '').isdigit()) / len(df.columns)

        if first_row_text_ratio > 0.5:  # If more than 50% of first row is text
            df.columns = df.iloc[0]
            df = df.drop(df.index[0]).reset_index(drop=True)

    return df

def merge_cross_page_tables(tables, paragraphs):
    """Merge tables that span across pages."""
    if len(tables) < 2:
        return tables, []

    merge_candidates = find_merge_table_candidates(tables)
    merged_tables = []
    merged_info = []
    skip_indices = set()

    for candidate in merge_candidates:
        table1_idx = candidate['table1_idx']
        table2_idx = candidate['table2_idx']

        if table1_idx in skip_indices or table2_idx in skip_indices:
            continue

        table1 = tables[table1_idx]
        table2 = tables[table2_idx]

        # Get span offsets to check for paragraphs between tables
        _, table1_end = get_table_span_offsets(table1)
        table2_start, _ = get_table_span_offsets(table2)

        # Check if there are meaningful paragraphs between the tables
        has_paragraphs = check_paragraph_presence(paragraphs, table1_end, table2_start)

        if not has_paragraphs:
            # Merge the tables
            df1 = convert_table_to_dataframe(table1)
            df2 = convert_table_to_dataframe(table2)

            if not df1.empty and not df2.empty and len(df1.columns) == len(df2.columns):
                # Align column names
                df2.columns = df1.columns
                merged_df = pd.concat([df1, df2], ignore_index=True)

                # Create merged table info
                merged_table = {
                    'dataframe': merged_df,
                    'pages': get_table_page_numbers(table1) + get_table_page_numbers(table2),
                    'merged_from': [table1_idx, table2_idx],
                    'page_break': candidate['page_break']
                }

                merged_tables.append(merged_table)
                merged_info.append(f"Tables {table1_idx + 1} and {table2_idx + 1} (pages {candidate['page_break']})")
                skip_indices.update([table1_idx, table2_idx])

    # Add non-merged tables
    for i, table in enumerate(tables):
        if i not in skip_indices:
            df = convert_table_to_dataframe(table)
            if not df.empty:
                table_info = {
                    'dataframe': df,
                    'pages': get_table_page_numbers(table),
                    'merged_from': [i],
                    'page_break': None
                }
                merged_tables.append(table_info)

    return merged_tables, merged_info

def analyze_document(file_path: Path, client: DocumentIntelligenceClient) -> Optional[Any]:
    """Analyze document using Azure Document Intelligence."""
    try:
        with open(file_path, "rb") as f:
            document_content = f.read()

        # Start analysis
        poller = client.begin_analyze_document(
            "prebuilt-layout",  # Using prebuilt layout model
            document_content,
            content_type="application/octet-stream"
        )

        # Get result - return the raw result object, not converted to dict
        result = poller.result()
        return result

    except Exception as e:
        st.error(f"Error analyzing document: {str(e)}")
        return None

def display_enhanced_analysis_results(analysis_result):
    """Display enhanced analysis results with proper formatting."""
    if not analysis_result:
        st.warning("No analysis results to display.")
        return

    # Create tabs for different types of content
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 Tables", "📝 Paragraphs", "🔑 Key-Value Pairs", "📄 Pages", "🔍 Raw Data"])

    with tab1:
        display_enhanced_tables(analysis_result)

    with tab2:
        display_paragraphs_by_role(analysis_result)

    with tab3:
        display_key_value_pairs(analysis_result)

    with tab4:
        display_page_breakdown(analysis_result)

    with tab5:
        display_raw_results(analysis_result)

def display_enhanced_tables(analysis_result):
    """Display tables with enhanced formatting and cross-page merging."""
    if not hasattr(analysis_result, 'tables') or not analysis_result.tables:
        st.info("No tables found in the document.")
        return

    st.subheader("📊 Enhanced Table Analysis")

    # Get paragraphs for cross-page analysis
    paragraphs = getattr(analysis_result, 'paragraphs', [])

    # Process tables with cross-page merging
    processed_tables, merge_info = merge_cross_page_tables(analysis_result.tables, paragraphs)

    if merge_info:
        st.success(f"🔗 Merged {len(merge_info)} cross-page table(s):")
        for info in merge_info:
            st.write(f"• {info}")
        st.write("---")

    # Display processed tables
    for i, table_info in enumerate(processed_tables):
        df = table_info['dataframe']
        pages = table_info['pages']
        merged_from = table_info['merged_from']
        page_break = table_info['page_break']

        # Table header
        if page_break:
            st.write(f"**📋 Merged Table {i+1}** (Original tables: {', '.join(map(str, [x+1 for x in merged_from]))}, Pages: {', '.join(map(str, pages))})")
            st.caption(f"🔗 Merged across page break: {page_break}")
        else:
            original_idx = merged_from[0] + 1
            st.write(f"**📋 Table {i+1}** (Original: Table {original_idx}, Page: {', '.join(map(str, pages))})")

        # Display table with enhanced formatting
        if not df.empty:
            # Add download button for each table
            csv = df.to_csv(index=False)
            st.download_button(
                label=f"📥 Download Table {i+1} as CSV",
                data=csv,
                file_name=f"table_{i+1}.csv",
                mime="text/csv",
                key=f"download_table_{i}"
            )

            # Display the dataframe with better formatting
            st.dataframe(
                df,
                use_container_width=True,
                hide_index=True
            )
        else:
            st.warning("Table structure could not be parsed properly.")

        st.write("---")

def display_paragraphs_by_role(analysis_result):
    """Display paragraphs organized by their roles."""
    if not hasattr(analysis_result, 'paragraphs') or not analysis_result.paragraphs:
        st.info("No paragraphs found in the document.")
        return

    st.subheader("📝 Document Structure Analysis")

    # Group paragraphs by role
    role_groups = {}
    for paragraph in analysis_result.paragraphs:
        role = getattr(paragraph, 'role', 'content')
        if role not in role_groups:
            role_groups[role] = []
        role_groups[role].append(paragraph)

    # Display each role group
    role_icons = {
        'title': '📌',
        'sectionHeading': '📋',
        'pageHeader': '🔝',
        'pageFooter': '🔽',
        'pageNumber': '🔢',
        'content': '📄'
    }

    for role, paragraphs in role_groups.items():
        icon = role_icons.get(role, '📄')
        st.write(f"**{icon} {role.title()} ({len(paragraphs)} items)**")

        with st.expander(f"View {role} content", expanded=(role in ['title', 'sectionHeading'])):
            for i, paragraph in enumerate(paragraphs):
                content = getattr(paragraph, 'content', '')
                if content.strip():
                    # Get page information if available
                    page_info = ""
                    if hasattr(paragraph, 'bounding_regions') and paragraph.bounding_regions:
                        pages = [region.page_number for region in paragraph.bounding_regions]
                        page_info = f" (Page {', '.join(map(str, pages))})"

                    st.write(f"{i+1}. {content}{page_info}")
        st.write("---")

def display_key_value_pairs(analysis_result):
    """Display key-value pairs with enhanced formatting."""
    if not hasattr(analysis_result, 'key_value_pairs') or not analysis_result.key_value_pairs:
        st.info("No key-value pairs found in the document.")
        return

    st.subheader("🔑 Form Fields & Key-Value Pairs")

    # Create a DataFrame for better display
    kv_data = []
    for kv in analysis_result.key_value_pairs:
        key_content = ""
        value_content = ""
        confidence = 0

        if hasattr(kv, 'key') and kv.key:
            key_content = getattr(kv.key, 'content', '')

        if hasattr(kv, 'value') and kv.value:
            value_content = getattr(kv.value, 'content', '')
            confidence = getattr(kv.value, 'confidence', 0)

        kv_data.append({
            'Key': key_content,
            'Value': value_content,
            'Confidence': f"{confidence:.2%}" if confidence > 0 else "N/A"
        })

    if kv_data:
        df_kv = pd.DataFrame(kv_data)
        st.dataframe(df_kv, use_container_width=True, hide_index=True)

        # Download button
        csv = df_kv.to_csv(index=False)
        st.download_button(
            label="📥 Download Key-Value Pairs as CSV",
            data=csv,
            file_name="key_value_pairs.csv",
            mime="text/csv"
        )

def display_page_breakdown(analysis_result):
    """Display content breakdown by page."""
    st.subheader("📄 Page-by-Page Analysis")

    # Get page count
    page_count = 1
    if hasattr(analysis_result, 'pages') and analysis_result.pages:
        page_count = len(analysis_result.pages)

    st.write(f"**Total Pages:** {page_count}")

    # Page selector
    if page_count > 1:
        selected_page = st.selectbox("Select page to analyze:", range(1, page_count + 1))

        # Filter content by page
        page_content = {
            'tables': [],
            'paragraphs': [],
            'key_value_pairs': []
        }

        # Filter tables
        if hasattr(analysis_result, 'tables'):
            for table in analysis_result.tables:
                table_pages = get_table_page_numbers(table)
                if selected_page in table_pages:
                    page_content['tables'].append(table)

        # Filter paragraphs
        if hasattr(analysis_result, 'paragraphs'):
            for paragraph in analysis_result.paragraphs:
                if hasattr(paragraph, 'bounding_regions'):
                    para_pages = [region.page_number for region in paragraph.bounding_regions]
                    if selected_page in para_pages:
                        page_content['paragraphs'].append(paragraph)

        # Display page content summary
        st.write(f"**Page {selected_page} contains:**")
        st.write(f"• {len(page_content['tables'])} table(s)")
        st.write(f"• {len(page_content['paragraphs'])} paragraph(s)")
    else:
        st.info("Single page document - all content is on page 1.")

def display_raw_results(analysis_result):
    """Display raw analysis results for debugging."""
    st.subheader("🔍 Raw Analysis Data")

    # Convert result to dictionary for JSON display
    try:
        if hasattr(analysis_result, 'as_dict'):
            result_dict = analysis_result.as_dict()
        else:
            # Fallback: convert to dict manually
            result_dict = {
                'content': getattr(analysis_result, 'content', ''),
                'pages': len(getattr(analysis_result, 'pages', [])),
                'tables': len(getattr(analysis_result, 'tables', [])),
                'paragraphs': len(getattr(analysis_result, 'paragraphs', [])),
                'key_value_pairs': len(getattr(analysis_result, 'key_value_pairs', []))
            }

        st.json(result_dict)
    except Exception as e:
        st.error(f"Could not display raw results: {str(e)}")
        st.write("**Analysis Result Object:**")
        st.write(str(analysis_result))

def main():
    """Main Streamlit application."""
    st.title("📄 Document Intelligence Preview")
    st.markdown("Select a document to preview it and see the extracted data from Azure Document Intelligence.")
    
    # Sidebar for file selection
    st.sidebar.header("📁 Document Selection")
    
    # Get available files
    available_files = get_document_files()
    
    if not available_files:
        st.error(f"No supported documents found in '{DOCUMENTS_FOLDER}' folder.")
        st.info("Supported file types: PDF, PNG, JPG, JPEG, DOCX, ODT")
        return
    
    # File selection
    selected_file = st.sidebar.selectbox(
        "Choose a document:",
        available_files,
        index=0
    )
    
    if not selected_file:
        st.info("Please select a document to preview.")
        return
    
    file_path = Path(DOCUMENTS_FOLDER) / selected_file
    
    # Display file info
    st.sidebar.write(f"**Selected:** {selected_file}")
    st.sidebar.write(f"**Size:** {file_path.stat().st_size / 1024:.1f} KB")
    
    # Main content area with two columns
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📄 Document Preview")
        display_document(file_path)
    
    with col2:
        st.header("🤖 Document Intelligence Analysis")
        
        # Check if Azure credentials are available
        client = get_azure_client()
        
        if not client:
            st.warning("⚠️ Azure Document Intelligence credentials not configured.")
            st.info("""
            To enable document analysis, please set up your Azure credentials:
            
            **Option 1: Environment Variables**
            ```
            AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=your_endpoint
            AZURE_DOCUMENT_INTELLIGENCE_KEY=your_key
            ```
            
            **Option 2: Streamlit Secrets**
            Add to `.streamlit/secrets.toml`:
            ```
            AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT = "your_endpoint"
            AZURE_DOCUMENT_INTELLIGENCE_KEY = "your_key"
            ```
            """)
            return
        
        # Analyze button
        if st.button("🔍 Analyze Document", type="primary"):
            with st.spinner("Analyzing document..."):
                analysis_result = analyze_document(file_path, client)
                
                if analysis_result:
                    st.success("✅ Analysis completed!")
                    display_enhanced_analysis_results(analysis_result)
                else:
                    st.error("❌ Analysis failed. Please check your credentials and try again.")

if __name__ == "__main__":
    main()
