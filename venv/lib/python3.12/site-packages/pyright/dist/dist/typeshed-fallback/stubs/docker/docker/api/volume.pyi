from typing import Any

class VolumeApiMixin:
    def volumes(self, filters: dict[str, Any] | None = None) -> dict[str, Any]: ...
    def create_volume(
        self,
        name: str | None = None,
        driver: str | None = None,
        driver_opts: dict[str, Any] | None = None,
        labels: dict[str, Any] | None = None,
    ) -> dict[str, Any]: ...
    def inspect_volume(self, name: str) -> dict[str, Any]: ...
    def prune_volumes(self, filters: dict[str, Any] | None = None) -> dict[str, Any]: ...
    def remove_volume(self, name, force: bool = False) -> None: ...
