from collections.abc import Iterable
from typing import NoReturn, Protocol, type_check_only

from docker import APIClient
from docker.models.configs import ConfigCollection
from docker.models.containers import ContainerCollection
from docker.models.images import ImageCollection
from docker.models.networks import NetworkCollection
from docker.models.nodes import NodeCollection
from docker.models.plugins import PluginCollection
from docker.models.secrets import SecretCollection
from docker.models.services import ServiceCollection
from docker.models.swarm import Swarm
from docker.models.volumes import VolumeCollection

@type_check_only
class _Environ(Protocol):
    def __getitem__(self, k: str, /) -> str: ...
    def keys(self) -> Iterable[str]: ...

class DockerClient:
    api: APIClient
    def __init__(self, *args, **kwargs) -> None: ...
    @classmethod
    def from_env(
        cls,
        *,
        version: str | None = None,
        timeout: int = ...,
        max_pool_size: int = ...,
        environment: _Environ | None = None,
        use_ssh_client: bool = False,
    ) -> DockerClient: ...
    @property
    def configs(self) -> ConfigCollection: ...
    @property
    def containers(self) -> ContainerCollection: ...
    @property
    def images(self) -> ImageCollection: ...
    @property
    def networks(self) -> NetworkCollection: ...
    @property
    def nodes(self) -> NodeCollection: ...
    @property
    def plugins(self) -> PluginCollection: ...
    @property
    def secrets(self) -> SecretCollection: ...
    @property
    def services(self) -> ServiceCollection: ...
    @property
    def swarm(self) -> Swarm: ...
    @property
    def volumes(self) -> VolumeCollection: ...
    def events(self, *args, **kwargs): ...
    def df(self): ...
    def info(self, *args, **kwargs): ...
    def login(self, *args, **kwargs): ...
    def ping(self, *args, **kwargs): ...
    def version(self, *args, **kwargs): ...
    def close(self): ...
    def __getattr__(self, name: str) -> NoReturn: ...

from_env = DockerClient.from_env
