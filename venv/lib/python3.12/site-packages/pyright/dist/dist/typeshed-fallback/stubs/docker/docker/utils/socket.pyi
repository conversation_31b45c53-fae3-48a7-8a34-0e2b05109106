from _typeshed import Incomplete, ReadableBuffer
from collections.abc import Generator, Iterable
from typing import Final, Literal, TypeVar, overload

_T = TypeVar("_T")

STDOUT: Final = 1
STDERR: Final = 2

class SocketError(Exception): ...

NPIPE_ENDED: Final = 109

def read(socket, n: int = 4096): ...
def read_exactly(socket, n: int) -> bytes: ...
def next_frame_header(socket) -> tuple[Incomplete, int]: ...
def frames_iter(socket, tty): ...
def frames_iter_no_tty(socket) -> Generator[tuple[str | Incomplete, str | bytes | Incomplete]]: ...
def frames_iter_tty(socket) -> Generator[Incomplete]: ...
@overload
def consume_socket_output(
    frames: Iterable[tuple[Incomplete, Incomplete]], demux: Literal[True]
) -> tuple[Incomplete, Incomplete]: ...
@overload
def consume_socket_output(frames: Iterable[ReadableBuffer], demux: Literal[False] = False) -> bytes: ...
@overload
def demux_adaptor(stream_id: Literal[1], data: _T) -> tuple[_T, None]: ...
@overload
def demux_adaptor(stream_id: Literal[2], data: _T) -> tuple[None, _T]: ...
