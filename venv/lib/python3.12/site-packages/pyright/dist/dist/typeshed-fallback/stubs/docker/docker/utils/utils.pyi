import datetime
from _typeshed import <PERSON>D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>omplete, ReadableBuffer
from collections.abc import Iterable, Mapping
from shlex import _ShlexInstream
from typing import Literal, NamedTuple, NoReturn, TypedDict, TypeVar, overload, type_check_only
from typing_extensions import deprecated

from ..tls import TLSConfig

_T = TypeVar("_T")
_K = TypeVar("_K")
_V = TypeVar("_V")

@type_check_only
class _EnvKWArgs(TypedDict, total=False):
    base_url: str
    tls: TLSConfig

class URLComponents(NamedTuple):
    scheme: str | None
    netloc: str | None
    url: str
    params: str | None
    query: str | None
    fragment: str | None

@deprecated("utils.create_ipam_pool has been removed. Please use a docker.types.IPAMPool object instead.")
def create_ipam_pool(*args, **kwargs) -> NoReturn: ...
@deprecated("utils.create_ipam_config has been removed. Please use a docker.types.IPAMConfig object instead.")
def create_ipam_config(*args, **kwargs) -> NoReturn: ...
def decode_json_header(header: str | ReadableBuffer): ...
def compare_version(v1: str, v2: str) -> Literal[0, -1, 1]: ...
def version_lt(v1: str, v2: str) -> bool: ...
def version_gte(v1: str, v2: str) -> bool: ...
def convert_port_bindings(
    port_bindings: Mapping[str, int | list[int] | tuple[str, int] | None],
) -> dict[str, list[dict[str, str]]]: ...
@overload
def convert_volume_binds(binds: list[_T]) -> list[_T]: ...
@overload
def convert_volume_binds(binds: Mapping[str | bytes, Incomplete]) -> list[str]: ...
@overload
def convert_tmpfs_mounts(tmpfs: dict[_K, _V]) -> dict[_K, _V]: ...
@overload
def convert_tmpfs_mounts(tmpfs: list[str]) -> dict[str, str]: ...
@overload
def convert_service_networks(networks: None) -> None: ...
@overload
def convert_service_networks(networks: list[str] | list[dict[str, str]] | list[str | dict[str, str]]) -> list[dict[str, str]]: ...
def parse_repository_tag(repo_name: str) -> tuple[str, str | None]: ...
@overload
def parse_host(addr: None, is_win32: Literal[True], tls: bool = False) -> Literal["npipe:////./pipe/docker_engine"]: ...
@overload
def parse_host(
    addr: None, is_win32: Literal[False] = False, tls: bool = False
) -> Literal["http+unix:///var/run/docker.sock"]: ...
@overload
def parse_host(addr: str | None, is_win32: bool = False, tls: bool = False) -> str | bytes: ...
def parse_devices(devices: Iterable[str | dict[str, Incomplete]]) -> list[dict[str, Incomplete]]: ...
def kwargs_from_env(environment: Mapping[str, Incomplete] | None = None) -> _EnvKWArgs: ...
def convert_filters(filters) -> str: ...
def datetime_to_timestamp(dt: datetime.datetime) -> int: ...
def parse_bytes(s: float | str) -> float: ...
def normalize_links(links: dict[str, str] | dict[str, None] | dict[str, str | None] | Iterable[tuple[str, str | None]]): ...
def parse_env_file(env_file: FileDescriptorOrPath) -> dict[str, str]: ...
def split_command(command: str | _ShlexInstream) -> list[str]: ...
def format_environment(environment: Mapping[str, object | None]) -> list[str]: ...
def format_extra_hosts(
    extra_hosts: Mapping[object, object], task: bool = False  # keys and values are converted to str
) -> list[str]: ...
@deprecated("utils.create_host_config has been removed. Please use a docker.types.HostConfig object instead.")
def create_host_config(self, *args, **kwargs) -> NoReturn: ...
