from docutils import nodes
from docutils.parsers.rst import Directive

class BaseAdmonition(Directive):
    node_class: type[nodes.Admonition]  # Subclasses must set this to the appropriate admonition node class.

class Admonition(BaseAdmonition): ...
class Attention(BaseAdmonition): ...
class Caution(BaseAdmonition): ...
class Danger(BaseAdmonition): ...
class Error(BaseAdmonition): ...
class Hint(BaseAdmonition): ...
class Important(BaseAdmonition): ...
class Note(BaseAdmonition): ...
class Tip(BaseAdmonition): ...
class Warning(BaseAdmonition): ...
