from enum import IntEnum
from typing import Final

class UnicodeScript(IntEnum):
    COMMON = 0
    LATIN = 1
    GREEK = 2
    CYRILLIC = 3
    ARMENIAN = 4
    HEBREW = 5
    ARABIC = 6
    SYRIAC = 7
    THAANA = 8
    DEVANAGARI = 9
    BENGALI = 10
    GURMUKHI = 11
    GUJARATI = 12
    ORIYA = 13
    TAMIL = 14
    TELUGU = 15
    KANNADA = 16
    MALAYALAM = 17
    SINHALA = 18
    THAI = 19
    LAO = 20
    TIBETAN = 21
    MYANMAR = 22
    GEORGIAN = 23
    HANGUL = 24
    ETHIOPIC = 25
    CHEROKEE = 26
    CANADIAN_ABORIGINAL = 27
    OGHAM = 28
    RUNIC = 29
    KHMER = 30
    MONGOLIAN = 31
    HIRAGANA = 32
    KATAKANA = 33
    BOPOMOFO = 34
    HAN = 35
    YI = 36
    OLD_ITALIC = 37
    GOTHIC = 38
    DESERET = 39
    INHERITED = 40
    TAGALOG = 41
    HANUNOO = 42
    BUHID = 43
    TAGBANWA = 44
    LIMBU = 45
    TAI_LE = 46
    LINEAR_B = 47
    UGARITIC = 48
    SHAVIAN = 49
    OSMANYA = 50
    CYPRIOT = 51
    BRAILLE = 52
    BUGINESE = 53
    COPTIC = 54
    NEW_TAI_LUE = 55
    GLAGOLITIC = 56
    TIFINAGH = 57
    SYLOTI_NAGRI = 58
    OLD_PERSIAN = 59
    KHAROSHTHI = 60
    BALINESE = 61
    CUNEIFORM = 62
    PHOENICIAN = 63
    PHAGS_PA = 64
    NKO = 65
    SUNDANESE = 66
    LEPCHA = 67
    OL_CHIKI = 68
    VAI = 69
    SAURASHTRA = 70
    KAYAH_LI = 71
    REJANG = 72
    LYCIAN = 73
    CARIAN = 74
    LYDIAN = 75
    CHAM = 76
    TAI_THAM = 77
    TAI_VIET = 78
    AVESTAN = 79
    EGYPTIAN_HIEROGLYPHS = 80
    SAMARITAN = 81
    LISU = 82
    BAMUM = 83
    JAVANESE = 84
    MEETEI_MAYEK = 85
    IMPERIAL_ARAMAIC = 86
    OLD_SOUTH_ARABIAN = 87
    INSCRIPTIONAL_PARTHIAN = 88
    INSCRIPTIONAL_PAHLAVI = 89
    OLD_TURKIC = 90
    KAITHI = 91
    BATAK = 92
    BRAHMI = 93
    MANDAIC = 94
    CHAKMA = 95
    MEROITIC_CURSIVE = 96
    MEROITIC_HIEROGLYPHS = 97
    MIAO = 98
    SHARADA = 99
    SORA_SOMPENG = 100
    TAKRI = 101
    CAUCASIAN_ALBANIAN = 102
    BASSA_VAH = 103
    DUPLOYAN = 104
    ELBASAN = 105
    GRANTHA = 106
    PAHAWH_HMONG = 107
    KHOJKI = 108
    LINEAR_A = 109
    MAHAJANI = 110
    MANICHAEAN = 111
    MENDE_KIKAKUI = 112
    MODI = 113
    MRO = 114
    OLD_NORTH_ARABIAN = 115
    NABATAEAN = 116
    PALMYRENE = 117
    PAU_CIN_HAU = 118
    OLD_PERMIC = 119
    PSALTER_PAHLAVI = 120
    SIDDHAM = 121
    KHUDAWADI = 122
    TIRHUTA = 123
    WARANG_CITI = 124
    AHOM = 125
    ANATOLIAN_HIEROGLYPHS = 126
    HATRAN = 127
    MULTANI = 128
    OLD_HUNGARIAN = 129
    SIGNWRITING = 130
    ADLAM = 131
    BHAIKSUKI = 132
    MARCHEN = 133
    NEWA = 134
    OSAGE = 135
    TANGUT = 136
    MASARAM_GONDI = 137
    NUSHU = 138
    SOYOMBO = 139
    ZANABAZAR_SQUARE = 140
    DOGRA = 141
    GUNJALA_GONDI = 142
    MAKASAR = 143
    MEDEFAIDRIN = 144
    HANIFI_ROHINGYA = 145
    SOGDIAN = 146
    OLD_SOGDIAN = 147
    ELYMAIC = 148
    NANDINAGARI = 149
    NYIAKENG_PUACHUE_HMONG = 150
    WANCHO = 151
    CHORASMIAN = 152
    DIVES_AKURU = 153
    KHITAN_SMALL_SCRIPT = 154
    YEZIDI = 155
    CYPRO_MINOAN = 156
    OLD_UYGHUR = 157
    TANGSA = 158
    TOTO = 159
    VITHKUQI = 160
    KAWI = 161
    NAG_MUNDARI = 162
    UNKNOWN = 999

UNICODE_RANGE_TO_SCRIPT: Final[tuple[tuple[int, int, int]]]

def get_unicode_script(char: str) -> UnicodeScript: ...
