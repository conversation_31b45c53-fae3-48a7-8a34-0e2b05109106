from collections.abc import Callable, Iterable

import gdb
from gdb import _PrettyPrinterLookupFunction

class PrettyPrinter:
    name: str
    subprinters: list[SubPrettyPrinter] | None
    enabled: bool

    def __init__(self, name: str, subprinters: Iterable[SubPrettyPrinter] | None = ...) -> None: ...
    def __call__(self, val: gdb.Value) -> gdb._PrettyPrinter | None: ...

class SubPrettyPrinter:
    name: str
    enabled: bool

    def __init__(self, name: str) -> None: ...

class RegexpCollectionPrettyPrinter(PrettyPrinter):
    def __init__(self, name: str) -> None: ...
    def add_printer(self, name: str, regexp: str, gen_printer: _PrettyPrinterLookupFunction) -> None: ...

class FlagEnumerationPrinter(PrettyPrinter):
    def __init__(self, enum_type: str) -> None: ...

class NoOpArrayPrinter(gdb.ValuePrinter):
    def __init__(self, ty, value) -> None: ...
    def child(self, i): ...
    def children(self): ...
    def display_hint(self): ...
    def num_children(self): ...
    def to_string(self) -> str: ...

class NoOpPointerReferencePrinter(gdb.ValuePrinter):
    def __init__(self, value) -> None: ...
    def child(self, i): ...
    def children(self): ...
    def num_children(self): ...
    def to_string(self) -> str: ...

class NoOpScalarPrinter(gdb.ValuePrinter):
    def __init__(self, value) -> None: ...
    def to_string(self) -> str: ...

class NoOpStructPrinter(gdb.ValuePrinter):
    def __init__(self, ty, value) -> None: ...
    def children(self): ...
    def to_string(self) -> str: ...

def register_pretty_printer(
    obj: gdb.Objfile | gdb.Progspace | None,
    printer: PrettyPrinter | Callable[[gdb.Value], gdb._PrettyPrinter | None],
    replace: bool = ...,
) -> None: ...
def make_visualizer(value: gdb.Value): ...
