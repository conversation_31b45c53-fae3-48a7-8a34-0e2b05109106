version = "25.4.*"
upstream_repository = "https://github.com/gevent/gevent"
requires = ["types-greenlet", "types-psutil"]

[tool.stubtest]
# Run stubtest on all platforms, since there is some platform specific stuff
# especially in the stdlib module replacement
platforms = ["linux", "darwin", "win32"]
# for testing the ffi loop implementations on all platforms
stubtest_requirements = ["cffi", "dnspython"]
apt_dependencies = ["libev4", "libev-dev", "libuv1", "libuv1-dev"]
brew_dependencies = ["libev", "libuv"]
