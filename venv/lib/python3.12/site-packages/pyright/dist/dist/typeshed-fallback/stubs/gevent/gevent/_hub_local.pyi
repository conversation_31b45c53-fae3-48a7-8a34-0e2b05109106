from gevent._types import _Loop
from gevent.hub import Hub as _Hub

__all__ = ["get_hub", "get_hub_noargs", "get_hub_if_exists"]

Hub: type[_Hub] | None

def get_hub_class() -> type[_Hub] | None: ...
def set_default_hub_class(hubtype: type[_Hub]) -> None: ...
def get_hub() -> _Hub: ...
def get_hub_noargs() -> _Hub: ...
def get_hub_if_exists() -> _Hub | None: ...
def set_hub(hub: _Hub) -> None: ...
def get_loop() -> _Loop: ...
def set_loop(loop: _Loop) -> None: ...
