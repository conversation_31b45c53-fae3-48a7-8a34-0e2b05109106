from gevent._ffi import watcher as _base
from gevent._types import _IoWatcher

class watcher(_base.watcher):
    @property
    def ref(self) -> bool: ...
    @ref.setter
    def ref(self, value: bool) -> None: ...

class io(_base.Io<PERSON><PERSON><PERSON>, watcher):
    EVENT_MASK: int
    @property
    def events(self) -> int: ...
    @events.setter
    def events(self, value: int) -> None: ...
    def multiplex(self, events: int) -> _IoWatcher: ...

class fork(_base.ForkMixin, watcher): ...
class child(_base.ChildMix<PERSON>, watcher): ...

# for some reason pending on this has been overwritten with None, but we don't
# necessarily want to change our Protocol to reflect that, so for now we ignore it
class async_(_base.AsyncMixin, watcher): ...
class timer(_base.Timer<PERSON>ix<PERSON>, watcher): ...

class stat(_base.StatMixin, watcher):
    MIN_STAT_INTERVAL: float

class signal(_base.Signal<PERSON><PERSON><PERSON>, watcher): ...
class idle(_base.<PERSON><PERSON><PERSON><PERSON><PERSON>, watcher): ...
class check(_base.<PERSON><PERSON><PERSON><PERSON>, watcher): ...
class prepare(_base.PrepareMixin, watcher): ...

__all__: list[str] = []
